issues:
  - title: "[TASK] Evaluate Metabase VPN Access Requirements"
    description: |
      # Background
      Previously, Metabase was kept publicly accessible because some users who needed access were not from the PED group and didn't have VPN access. We need to reassess this situation to determine if we can move Metabase behind VPN for improved security.

      # Acceptance Criteria
      - Audit current Metabase users and identify their organizational groups
      - Determine which users are not from PED group and currently lack VPN access
      - Investigate process and requirements for granting VPN access to non-PED users
      - Assess security implications of current public access vs VPN-protected access
      - Document findings on user access patterns and VPN eligibility
      - Evaluate alternative access solutions if VPN is not feasible for all users
      - Provide recommendation on whether to move Metabase behind VPN
      - Create implementation plan if VPN migration is recommended
      - Consider impact on user workflows and access convenience

    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "1310f4c8-55b4-42da-9a3b-45a71f8155e5"
    projectId: "cd02dae1-b8a0-4f01-8026-4d55b9000c94"
    milestoneId: "0bd6b306-a04c-448a-bf09-48f297a6c84e"